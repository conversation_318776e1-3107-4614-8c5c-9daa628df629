<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>TAS Admin – Dashboard</title>

    <!-- favicon & css -->
    <link rel="icon" href="/images/bannerShield2.png" type="image/png" />
    <link rel="stylesheet" href="/styles2.css" />
    <link rel="stylesheet" href="/css/footer-module.css" />
    <link rel="stylesheet" href="/css/button-module.css" />
    <link rel="stylesheet" href="/css/typography-module.css" />
    <link rel="stylesheet" href="/css/animation-module.css" />
    <link rel="stylesheet" href="/header-banner.css" />
    <link rel="stylesheet" href="/css/nav.css" />
    <link rel="stylesheet" href="/css/admin-forms.css" />
    <link rel="stylesheet" href="/css/admin-tables.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <script src="/responsive-helper.js" defer=""></script>
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>

    <!-- ====================================================================== -->
    <!--                          ADMIN DASHBOARD STYLES                       -->
    <!-- ====================================================================== -->

    <!--
        Admin Dashboard Styles - PARTIALLY EXTERNALIZED

        @description Some CSS styles moved to separate modules for better organization
        @modules
          - admin-forms.css: Basic form containers and controls
          - admin-tables.css: Basic table layouts
        @remaining Some complex styles remain embedded for now (tabs, specific overrides)
        @benefits Improved maintainability and AI comprehension
    -->

    <style>
      /* Remaining embedded styles for complex admin functionality */

      /* ================================================================== */
      /*                    ADMIN FORM CONTAINERS                           */
      /* ================================================================== */

      /**
         * Admin Form Container System
         *
         * @description Lilac-themed containers for all admin forms
         * @usage Applied to both content management and tutor management forms
         * @styling Consistent lilac background (#C8A2C8) with rounded corners
         * @responsive Maintains max-width of 700px with auto margins
         */

      /* lilac panel used by both forms */
      .admin-form-container {
        max-width: 700px;
        margin: 40px auto;
        background: #c8a2c8;
        padding: 20px 30px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }

      .admin-form-container h3 {
        margin-top: 0;
      }

      /* ================================================================== */
      /*                    FORM CONTROLS STYLING                          */
      /* ================================================================== */

      /**
         * Form Controls System
         *
         * @description Consistent styling for all form elements
         * @components Labels, inputs, textareas, selects, buttons
         * @styling Blue theme (#0057B7) with hover effects and transitions
         * @accessibility Proper focus states and cursor indicators
         */

      form label {
        display: block;
        margin-top: 14px;
        font-weight: bold;
      }

      form input,
      form textarea,
      form select {
        width: 100%;
        padding: 9px;
        margin-top: 4px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }

      form button {
        margin-top: 20px;
        padding: 12px 20px;
        background: #0057b7;
        color: #fff;
        border: 0;
        border-radius: 4px;
        font-size: 1.05em;
        cursor: pointer;
        transition: 0.25s;
      }

      form button:hover {
        background: #0046a5;
        box-shadow: 0 0 8px #c8a2c8;
      }

      /* Style for the cancel button */
      form button.cancel-btn {
        background: #6c757d;
        margin-left: 10px;
      }
      form button.cancel-btn:hover {
        background: #5a6268;
      }

      /* small table that lists existing dynamic sections */
      table.admin-table {
        width: 100%;
        margin-top: 25px;
        border-collapse: collapse;
        font-size: 0.93em;
      }

      .admin-table th,
      .admin-table td {
        border: 1px solid #ddd;
        padding: 6px;
      }

      .admin-table th {
        background: #ececec;
        text-align: left;
      }

      .admin-table button {
        background: none;
        border: 0;
        font-size: 1.2em;
        cursor: pointer;
        margin: 0 5px;
        padding: 0;
      }

      .admin-table button:hover {
        color: #b00;
      }

      /* Tabs styling */
      .tabs-container {
        max-width: 700px;
        margin: 20px auto;
      }

      .tab-buttons {
        display: flex;
        margin-bottom: 0;
      }

      .tab-button {
        flex: 1;
        padding: 12px 20px;
        background: #e6f0ff;
        color: #0057b7;
        border: 1px solid #0057b7;
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        font-size: 1.05em;
        cursor: pointer;
        transition: 0.25s;
        text-align: center;
        font-weight: bold;
      }

      .tab-button.active {
        background: #0057b7;
        color: white;
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      .admin-table {
        table-layout: fixed; /* cells never grow beyond 100 % */
        width: 100%;
      }

      .admin-table th,
      .admin-table td {
        overflow-wrap: anywhere; /* let the browser insert soft-wraps */
        word-break: break-word; /* older fallback, same idea        */
      }

      /* Tutor table specific styles */
      #tutorTable img {
        max-width: 60px;
        max-height: 60px;
        border-radius: 4px;
      }

      .tutor-subjects {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .tutor-subjects:hover {
        white-space: normal;
        overflow: visible;
      }
    </style>
  </head>

  <body data-page="admin">
    <!-- ====================================================================== -->
    <!--                          ADMIN DASHBOARD HTML                         -->
    <!-- ====================================================================== -->

    <!--
        Admin Dashboard Structure

        @description Main admin interface for content and tutor management
        @sections
          - Header/Banner: Site branding and navigation links
          - Tab Navigation: Switch between content and tutor management
          - Content Management: Dynamic sections, pages, and blog posts
          - Tutor Management: CRUD operations for tutor profiles
        @dependencies upload-helper.js, dynamic-sections.js
        @api-endpoints Multiple endpoints for content and tutor operations
    -->

    <!-- ─────────────── HEADER/BANNER ─────────────── -->
    <header>
      <h1 data-ve-block-id="0a085027-3a19-49be-87ae-b06a6e36d043">Tutors Alliance Scotland</h1>
      <div class="header-links">
        <a class="banner-login-link login-box" href="/" data-ve-block-id="a2c3c663-5d9d-4b5b-b630-e017bf8340b4">Home</a>
        <a
          class="banner-login-link login-box"
          href="/login.html?role=admin"
          data-ve-block-id="ee305c05-9d20-4871-927f-7dfc04e21884"
          >Login</a
        >
      </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <div class="rolling-banner">
      <div class="rolling-content" id="tutorBanner"></div>
    </div>

    <!-- ─────────────── MAIN CONTENT ─────────────── -->
    <main>
      <!-- Tabs Navigation -->
      <div class="tabs-container">
        <div class="tab-buttons">
          <button class="tab-button active" data-tab="content-tab">Manage Content</button>
          <button class="tab-button" data-tab="tutors-tab">Manage Tutors</button>
        </div>
      </div>

      <!-- Content Tab -->
      <div id="content-tab" class="tab-content active">
        <!-- ░░░░░░  ADD‑/DELETE SECTION  ░░░░░░ -->
        <div class="admin-form-container">
          <!-- Heading will change dynamically -->
          <h3 id="sectionFormHeading" data-ve-block-id="8eee3405-0831-4242-b51f-c00b65269345">Add a Dynamic Section</h3>

          <!-- ★ NEW – simplified form : only page, heading, paragraph, image ★ -->
          <form id="addSection" enctype="multipart/form-data" autocomplete="off">
            <label>
              Target Page:
              <select name="page" id="pageSelect">
                <!-- Generated dynamically from pages.js -->
              </select>
              <a
                id="viewPageLink"
                href="/"
                target="_blank"
                style="margin-left: 12px; font-size: 0.9em"
                data-ve-block-id="58511f1e-05ce-4b1b-a7b1-04dd59d69b20"
                >🔗 Open Page</a
              >
            </label>

            <!-- [MOVE-SECTION] ▸ Hidden unless editing -->
            <label
              id="movePageRow"
              style="
                display: none;
                margin-top: 10px;
                padding: 8px;
                background: #f0f8ff;
                border-radius: 4px;
                border-left: 3px solid #4a90e2;
              "
            >
              <span style="font-weight: 500; color: #2c5aa0">📍 Move this section to:</span>
              <select name="targetPage" id="movePageSelect" style="margin-left: 8px">
                <!-- Populated dynamically -->
              </select>
              <small style="display: block; margin-top: 4px; color: #666; font-style: italic">
                Section will be moved to the selected page and admin view will switch automatically.<br />
                <strong>Note:</strong> You may need to reorder sections on the target page after moving.
              </small>
            </label>

            <!-- ▸▸▸ Rolling-banner ONLY fields (stays visible at all times) -->
            <div id="rollingBannerFields" style="display: none">
              <label
                >News Content:
                <textarea
                  name="rollingText"
                  rows="3"
                  required=""
                  placeholder="Enter news content to display in the rolling banner"
                ></textarea>
              </label>
            </div>

            <!-- ▸▸▸ Meta controls – hidden for banner -->
            <div id="metaControls">
              <label
                >Layout:
                <select name="layout" id="sectionLayout">
                  <option value="standard" selected="">Standard</option>
                  <option value="team">Team Members Grid</option>
                  <option value="list">List Section</option>
                  <option value="testimonial">Testimonial Section</option>
                </select>
              </label>

              <!-- ★ Navigation integration controls (always visible) -->
              <label style="display: flex; align-items: center">
                <input type="checkbox" name="showInNav" id="showInNav" style="width: auto; margin-right: 8px" />
                <span>Add a link to the navigation bar</span>
              </label>

              <label id="navCatRow" style="margin-top: 10px; display: none">
                Show under:
                <select name="navCategory" id="sectionNavCategory">
                  <option value="tutors">For Tutors</option>
                  <option value="parents">For Parents</option>
                  <option value="about" selected="">About TAS</option>
                </select>
              </label>

              <!-- ★ ONE shared heading field, outside the per-layout wrappers -->
              <label id="mainHeadingLabel">Heading:<input name="heading" required="" /></label>

              <div id="standardFields">
                <!-- ★ Wrapper holds only the controls that belong to the Standard layout -->
                <div id="standardOnlyFields">
                  <label>Paragraph:<textarea name="text" rows="3" required=""></textarea></label>
                </div>
                <!-- /standardOnlyFields -->
              </div>

              <!-- ★ SHARED: Image and button fields available for all layout types -->
              <div id="sharedFields">
                <label>Image:<input type="file" name="image" id="sectionImage" accept="image/*" /></label>
                <!-- To show current image and allow removal -->
                <div id="currentImagePreview" style="margin-top: 10px; display: none">
                  <p style="margin-bottom: 5px" data-ve-block-id="09d85e4c-83d0-4bb1-836e-f6fb414b5946">
                    Current Image:
                  </p>
                  <img
                    src=""
                    alt="Current Image"
                    style="max-width: 100px; max-height: 100px; border-radius: 4px"
                    data-ve-block-id="0a1cfcab-196e-4c69-8198-94c82e8f690e"
                  />
                  <label style="display: inline-flex; align-items: center; margin-left: 15px">
                    <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 5px" />
                    Remove Image
                  </label>
                </div>

                <!-- ★ NEW: Add button fields -->
                <hr style="border-top: 1px solid #b3a1b3; margin: 20px 0" />
                <label>Button Label (Optional): <input name="buttonLabel" placeholder="e.g. Learn More" /></label>
                <label
                  >Button URL (Optional): <input name="buttonUrl" type="url" placeholder="https://example.com"
                /></label>

                <!-- ★ NEW: Checkbox to remove button, shown only during edit -->
                <label id="removeButtonRow" style="display: none; align-items: center; margin-top: 10px">
                  <input type="checkbox" name="removeButton" value="true" style="width: auto; margin-right: 8px" />
                  Remove Button
                </label>
              </div>
              <!-- /sharedFields -->

              <!-- Team builder fields -->
              <div id="teamBuilder" style="display: none; margin-top: 15px">
                <!-- heading removed – we now reuse the global one -->
                <button type="button" id="addMemberBtn">➕ Add Team Member</button>
                <div id="teamMemberList"></div>
                <input type="hidden" name="team" id="teamData" />
              </div>

              <!-- List builder fields -->
              <div id="listBuilder" style="display: none; margin-top: 15px">
                <label>
                  Description (optional):
                  <textarea
                    name="listDescription"
                    id="listDescription"
                    rows="2"
                    placeholder="Brief description before the list..."
                  ></textarea>
                </label>
                <label>
                  List Type:
                  <select name="listType" id="listType">
                    <option value="unordered">Bullet Points (•)</option>
                    <option value="ordered">Numbered List (1, 2, 3...)</option>
                  </select>
                </label>
                <button type="button" id="addListItemBtn">➕ Add List Item</button>
                <div id="listItemsList"></div>
                <input type="hidden" name="listItems" id="listItemsData" />
              </div>

              <!-- Testimonial builder fields -->
              <div id="testimonialBuilder" style="display: none; margin-top: 15px">
                <button type="button" id="addTestimonialBtn">➕ Add Testimonial</button>
                <div id="testimonialList"></div>
                <input type="hidden" name="testimonials" id="testimonialsData" />
              </div>

              <label>
                Position on Page:
                <select name="position">
                  <option value="top">Top of Page</option>
                  <option value="middle">Middle of Page</option>
                  <option value="bottom" selected="">Bottom of Page</option>
                </select>
              </label>
            </div>
            <!-- /metaControls -->

            <!-- Buttons will be managed by JS -->
            <button type="submit" id="submitSectionBtn">Add Section</button>
            <button type="button" id="cancelEditBtn" class="cancel-btn" style="display: none">Cancel Edit</button>
          </form>

          <!-- ★ NEW – table of existing sections with a delete button ★ -->
          <h3 style="margin-top: 35px" data-ve-block-id="cae01088-0f78-4a09-a2dc-0d214075c4a2">
            Existing sections for this page
          </h3>
          <table id="sectionTable" class="admin-table">
            <thead>
              <tr>
                <th>#</th>
                <th>Heading</th>
                <th>Layout</th>
                <th>Position</th>
                <th>Image?</th>
                <th>Button?</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- filled by JS -->
            </tbody>
          </table>
        </div>

        <!-- ░░░░░░  CREATE NEW PAGE  ░░░░░░ -->
        <div class="admin-form-container">
          <h3 id="pageFormHeading" data-ve-block-id="43a4e36a-6975-46a8-beb1-94b6a0eb2908">Create a New Page</h3>

          <form id="pageForm" enctype="multipart/form-data" autocomplete="off">
            <label>
              Page URL (e.g., "summer-tutoring"):
              <input type="text" name="slug" id="pageSlug" required="" />
            </label>

            <label>Page Title:<input name="heading" required="" /></label>
            <label>Page Content:<textarea name="text" rows="6" required=""></textarea></label>

            <label>
              Show in Navigation Menu:
              <select name="navCategory" id="pageNavCategory">
                <option value="tutors">For Tutors</option>
                <option value="parents">For Parents</option>
                <option value="about" selected="">About TAS</option>
              </select>
            </label>

            <!-- ★ NEW: Add button fields -->
            <hr style="border-top: 1px solid #b3a1b3; margin: 20px 0" />
            <label>
              Button Label (optional):
              <input name="buttonLabel" placeholder="e.g. Get Started" />
            </label>
            <label>
              Button URL (optional):
              <input type="url" name="buttonUrl" placeholder="https://…" />
            </label>

            <label>Featured Image:<input type="file" name="image" id="pageImage" accept="image/*" /></label>

            <div id="currentPageImagePreview" style="display: none; margin-top: 10px"></div>

            <label>
              <input type="checkbox" name="isPublished" id="isPublished" checked="" />
              Publish page immediately
            </label>

            <!-- shown only while editing -->
            <div id="pageEditOptions" style="display: none; margin-top: 10px">
              <label style="display: flex; align-items: center">
                <input type="checkbox" name="removeButton" value="true" style="width: auto; margin-right: 8px" />
                Remove Button
              </label>
              <label style="display: flex; align-items: center; margin-top: 5px">
                <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 8px" />
                Remove Image
              </label>
            </div>

            <button type="submit" id="submitPageBtn">Create Page</button>
            <button type="button" id="cancelPageEditBtn" class="cancel-btn" style="display: none">Cancel Edit</button>
          </form>

          <h3 style="margin-top: 35px" data-ve-block-id="657dfb5e-7981-4780-8bab-bdcd5af52e68">Existing Pages</h3>
          <table id="pagesTable" class="admin-table">
            <thead>
              <tr>
                <th>Title</th>
                <th>URL</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- filled by JS -->
            </tbody>
          </table>
        </div>
      </div>

      <!-- Tutors Tab -->
      <div id="tutors-tab" class="tab-content">
        <!-- ░░░░░░ ADD‑TUTOR FORM ░░░░░░ -->
        <div class="admin-form-container">
          <h3 id="tutorFormHeading" data-ve-block-id="13e6c683-aa50-44ba-92dd-feee30f7c319">Add a Tutor</h3>

          <!-- handled completely in JS  -->
          <form id="tutorForm" autocomplete="off">
            <label>Tutor Name:<input type="text" name="name" required="" /></label>

            <label>
              Subjects (comma‑separated):
              <input type="text" name="subjects" required="" />
            </label>

            <label>
              Cost Range (e.g. £ for £, ££ for ££):
              <input type="text" name="costRange" required="" id="costRangeInput" />
            </label>

            <label>
              Badges (comma‑separated):
              <input type="text" name="badges" />
            </label>

            <label>
              Tutor Image:
              <input type="file" id="imageField" accept="image/*" />
            </label>

            <label>
              Contact (email or website):
              <input type="text" name="contact" />
            </label>

            <label>Description:<textarea name="description"></textarea></label>

            <label>
              Postcodes (comma‑separated):
              <input type="text" name="postcodes" />
            </label>

            <!-- Current Image Preview -->
            <div
              id="currentTutorImagePreview"
              style="
                display: none;
                margin-top: 10px;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f8f9fa;
              "
            >
              <p style="margin-bottom: 5px">Current Image:</p>
              <img src="" alt="Current Image" style="max-width: 150px; border-radius: 4px; margin-bottom: 10px" />
              <label style="display: inline-flex; align-items: center; margin-left: 15px">
                <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 5px" /> Remove
                Image
              </label>
            </div>

            <button type="submit" id="submitTutorBtn">Add Tutor</button>
            <button
              type="button"
              id="cancelTutorEditBtn"
              style="
                display: none;
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 10px;
              "
            >
              Cancel Edit
            </button>
          </form>
        </div>

        <!-- ░░░░░░ MANAGE TUTORS ░░░░░░ -->
        <div class="admin-form-container">
          <h3 data-ve-block-id="39d7ff9d-4901-48f1-86e1-0755df97f9ae">Manage Existing Tutors</h3>
          <p data-ve-block-id="f128aec9-bb79-4cbb-92e2-efce7b8f5367">
            Click on a tutor's name to view details. Use the delete button to remove a tutor.
          </p>

          <table id="tutorTable" class="admin-table">
            <thead>
              <tr>
                <th>Image</th>
                <th>Name</th>
                <th>Subjects</th>
                <th>Cost</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- Filled by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </main>

    <!-- ====================================================================== -->
    <!--                          ADMIN DASHBOARD JAVASCRIPT                   -->
    <!-- ====================================================================== -->

    <!--
        Admin Dashboard JavaScript Logic

        @description Main JavaScript module for admin dashboard functionality
        @structure
          - Module Imports: upload-helper.js, pages.js, dynamic-sections.js
          - Tutor Management: CRUD operations for tutor profiles
          - Content Management: Dynamic sections and page management
          - Form Handling: Form validation, submission, and reset logic
          - API Communication: Fetch operations to backend endpoints
          - UI State Management: Tab switching, form visibility, table updates
        @dependencies
          - upload-helper.js: Image upload functionality
          - pages.js: Page configuration and routing
          - dynamic-sections.js: Dynamic content management
        @api-endpoints
          - /api/tutors: Tutor data operations
          - /api/addTutor: Add new tutor
          - /api/sections: Dynamic section management
          - /api/pages: Page management operations
    -->

    <!-- ───────────────  MODULE SCRIPT  ─────────────── -->
    <script type="module">
      import { uploadImage } from '/js/upload-helper.js';
      import { PAGES } from '/js/pages.js';

      /* ── page‑guard: only admins allowed ───────────────────────── */
      const auth = await fetch('/api/protected?role=admin');
      if (!auth.ok) {
        location.href = '/login.html?role=admin';
        throw 0;
      }

      /* ── rolling banner text ──────────────────────────────────── */
      // First try to fetch news from sections API
      fetch('/api/sections?page=rolling-banner')
        .then(r => r.json())
        .then(sections => {
          if (sections && sections.length > 0) {
            // Format news information - join all text content with separator
            const text = sections.map(s => s.text).join(' | ');
            tutorBanner.textContent = text;
          } else {
            // Fallback to tutors if no news sections are found
            return fetch('/api/tutors?format=json')
              .then(r => r.json())
              .then(list => {
                tutorBanner.textContent = list.map(t => `${t.name} (${t.subjects.join(', ')})`).join(' | ');
              });
          }
        })
        .catch(console.error);

      /* ====================================================================== */
      /*                          TAB SYSTEM MANAGEMENT                        */
      /* ====================================================================== */

      /**
       * Tab Switching Functionality
       *
       * @description Handles switching between content and tutor management tabs
       * @elements .tab-button (clickable tabs), .tab-content (tab panels)
       * @behavior Removes active class from all tabs, adds to clicked tab
       * @accessibility Uses data-tab attribute to link buttons to content
       */

      /* ── tab switching functionality ──────────────────────────── */
      const tabButtons = document.querySelectorAll('.tab-button');
      const tabContents = document.querySelectorAll('.tab-content');

      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          // Remove active class from all buttons and contents
          tabButtons.forEach(btn => btn.classList.remove('active'));
          tabContents.forEach(content => content.classList.remove('active'));

          // Add active class to clicked button and corresponding content
          button.classList.add('active');
          const tabId = button.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
        });
      });

      /* ====================================================================== */
      /*                          TUTOR MANAGEMENT SYSTEM                      */
      /* ====================================================================== */

      /**
       * Tutor Management (CREATE/UPDATE)
       *
       * @description Complete CRUD operations for tutor profiles
       * @features
       *   - Create new tutor profiles with image upload
       *   - Edit existing tutor information
       *   - Delete tutor profiles with confirmation
       *   - Image management (upload, preview, remove)
       * @api-endpoints
       *   - GET /api/tutors: Fetch all tutors
       *   - POST /api/addTutor: Create new tutor
       *   - PUT /api/tutors/:id: Update existing tutor
       *   - DELETE /api/tutors/:id: Delete tutor
       * @dependencies upload-helper.js for image handling
       */

      /* ░░░ 1. TUTOR MANAGEMENT (CREATE/UPDATE) ░░░ */

      // Get form elements
      const tutorForm = document.getElementById('tutorForm');
      const tutorFormHeading = document.getElementById('tutorFormHeading');
      const submitTutorBtn = document.getElementById('submitTutorBtn');
      const cancelTutorEditBtn = document.getElementById('cancelTutorEditBtn');
      const currentTutorImagePreview = document.getElementById('currentTutorImagePreview');
      const imageField = document.getElementById('imageField');
      const removeImageCheckbox = tutorForm.querySelector('[name="removeImage"]');

      // Store all tutors for editing
      let allTutors = [];

      /* Helper: Reset form to create mode */
      function resetTutorForm() {
        tutorForm.reset();
        delete tutorForm.dataset.editId;
        tutorFormHeading.textContent = 'Add a Tutor';
        submitTutorBtn.textContent = 'Add Tutor';
        cancelTutorEditBtn.style.display = 'none';
        currentTutorImagePreview.style.display = 'none';
        if (removeImageCheckbox) removeImageCheckbox.checked = false;
      }

      /* Helper: Populate form for editing */
      function populateTutorForm(tutor) {
        tutorForm.name.value = tutor.name || '';
        tutorForm.subjects.value = (tutor.subjects || []).join(', ');
        // Convert __P__ back to £ for display in the form
        tutorForm.costRange.value = (tutor.costRange || '').replace(/__P__/g, '£');
        tutorForm.badges.value = (tutor.badges || []).join(', ');
        tutorForm.contact.value = tutor.contact || '';
        tutorForm.description.value = tutor.description || '';
        tutorForm.postcodes.value = (tutor.postcodes || []).join(', ');

        // Show current image if exists
        if (tutor.imagePath) {
          currentTutorImagePreview.style.display = 'block';
          currentTutorImagePreview.querySelector('img').src = tutor.imagePath;
        } else {
          currentTutorImagePreview.style.display = 'none';
        }

        if (removeImageCheckbox) removeImageCheckbox.checked = false;

        // Update UI for edit mode
        tutorFormHeading.textContent = 'Edit Tutor';
        submitTutorBtn.textContent = 'Update Tutor';
        cancelTutorEditBtn.style.display = 'inline-block';
        tutorForm.dataset.editId = tutor._id;
      }

      /* Cancel edit button */
      cancelTutorEditBtn.addEventListener('click', () => {
        resetTutorForm();
      });

      // Note: £ symbols are converted to __P__ only during form submission
      // This allows users to see £ in the form but stores __P__ in the database
      const costRangeInput = document.getElementById('costRangeInput');

      tutorForm.addEventListener('submit', async e => {
        e.preventDefault();

        const isEditing = !!tutorForm.dataset.editId;
        const tutorId = tutorForm.dataset.editId;

        const fd = new FormData(tutorForm);
        const csv = s =>
          s
            .split(',')
            .map(x => x.trim())
            .filter(Boolean);

        /* optional image upload */
        let uploadedImagePath = '';
        if (imageField.files[0]) {
          const f = imageField.files[0];
          if (f.size > 2 * 1024 * 1024) return alert('Image > 2 MB');
          try {
            uploadedImagePath = await uploadImage(f, 'tutors');
          } catch (err) {
            return alert(err.message);
          }
        }

        /* Determine method and URL based on mode */
        const method = isEditing ? 'PUT' : 'POST';
        const url = isEditing ? `/api/addTutor?id=${tutorId}` : '/api/addTutor';

        // Convert £ symbols to __P__ before sending to database
        let costRange = fd.get('costRange').trim();
        costRange = costRange.replace(/£/g, '__P__');

        const payload = {
          name: fd.get('name').trim(),
          subjects: csv(fd.get('subjects')),
          costRange: costRange,
          badges: csv(fd.get('badges')),
          contact: fd.get('contact').trim(),
          description: fd.get('description').trim(),
          postcodes: csv(fd.get('postcodes')),
          imagePath: uploadedImagePath || '',
          removeImage: removeImageCheckbox ? removeImageCheckbox.checked : false,
        };

        // For POST fallback, add editId to payload
        if (isEditing) {
          payload.editId = tutorId;
        }

        try {
          console.log('Sending tutor data to API:', payload);
          const r = await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
            credentials: 'include',
          });

          if (!r.ok) {
            // Try to parse the error response as JSON
            try {
              const errorData = await r.json();
              console.error('Error with tutor operation:', errorData);
              return alert(`Error: ${errorData.message || 'Unknown error'}`);
            } catch (parseError) {
              // If JSON parsing fails, use text response
              const errorText = await r.text();
              console.error('Error with tutor operation (text):', errorText);
              return alert(`Error: ${errorText || 'Unknown error'}`);
            }
          }

          const result = await r.json();
          console.log(`Tutor ${isEditing ? 'updated' : 'added'} successfully`);
          alert(`Tutor ${isEditing ? 'updated' : 'added'} successfully!`);

          resetTutorForm();
          loadTutors(); // Refresh the tutor list
        } catch (error) {
          console.error('Exception while processing tutor:', error);
          alert(`An error occurred: ${error.message}`);
        }
      });

      /* ░░░ LOAD TUTORS AND HANDLE EDIT/DELETE ░░░ */
      // Function to load tutors
      async function loadTutors() {
        try {
          const response = await fetch('/api/tutors?format=json');
          if (!response.ok) {
            throw new Error('Failed to fetch tutors');
          }

          const tutors = await response.json();
          allTutors = tutors; // Store for editing
          const tutorTableBody = document.querySelector('#tutorTable tbody');
          tutorTableBody.innerHTML = '';

          tutors.forEach(tutor => {
            const row = document.createElement('tr');
            row.dataset.id = tutor._id;

            // Image cell
            const imageCell = document.createElement('td');
            if (tutor.imagePath) {
              const img = document.createElement('img');
              img.src = tutor.imagePath;
              img.alt = tutor.name;
              imageCell.appendChild(img);
            } else {
              imageCell.textContent = 'No image';
            }
            row.appendChild(imageCell);

            // Name cell
            const nameCell = document.createElement('td');
            nameCell.textContent = tutor.name;
            row.appendChild(nameCell);

            // Subjects cell
            const subjectsCell = document.createElement('td');
            subjectsCell.className = 'tutor-subjects';
            subjectsCell.textContent = tutor.subjects.join(', ');
            row.appendChild(subjectsCell);

            // Cost cell
            const costCell = document.createElement('td');
            // Convert __P__ back to £ for display in the table
            costCell.textContent = (tutor.costRange || '').replace(/__P__/g, '£');
            row.appendChild(costCell);

            // Actions cell
            const actionsCell = document.createElement('td');

            const editButton = document.createElement('button');
            editButton.innerHTML = '✏️';
            editButton.title = 'Edit Tutor';
            editButton.className = 'edit-tutor';
            editButton.dataset.id = tutor._id;
            actionsCell.appendChild(editButton);

            const deleteButton = document.createElement('button');
            deleteButton.innerHTML = '🗑️';
            deleteButton.title = 'Delete Tutor';
            deleteButton.className = 'delete-tutor';
            deleteButton.dataset.id = tutor._id;
            actionsCell.appendChild(deleteButton);

            row.appendChild(actionsCell);
            row.dataset.id = tutor._id;

            tutorTableBody.appendChild(row);
          });
        } catch (error) {
          console.error('Error loading tutors:', error);
          alert('Failed to load tutors. Please try again.');
        }
      }

      /* Handle edit and delete actions with event delegation */
      document.querySelector('#tutorTable tbody').addEventListener('click', async e => {
        const tutorId = e.target.dataset.id;

        if (e.target.classList.contains('edit-tutor')) {
          // Find the tutor to edit
          const tutor = allTutors.find(t => t._id === tutorId);
          if (!tutor) {
            alert('Tutor not found for editing');
            return;
          }

          // Populate form and scroll to it
          populateTutorForm(tutor);
          tutorForm.scrollIntoView({ behavior: 'smooth' });
        } else if (e.target.classList.contains('delete-tutor')) {
          if (!confirm('Are you sure you want to delete this tutor?')) {
            return;
          }

          try {
            const response = await fetch(`/api/addTutor?id=${tutorId}`, {
              method: 'DELETE',
              credentials: 'include',
            });

            if (response.ok) {
              alert('Tutor deleted successfully!');
              loadTutors(); // Refresh the tutor list
            } else {
              const error = await response.text();
              alert(`Failed to delete tutor: ${error}`);
            }
          } catch (error) {
            console.error('Error deleting tutor:', error);
            alert('An error occurred while deleting the tutor.');
          }
        }
      });

      // Load tutors when the page loads
      loadTutors();

      /* ░░░ 2. ADD / EDIT / DELETE SECTIONS ░░░ */
      const pageSel = document.getElementById('pageSelect');
      const viewPageLink = document.getElementById('viewPageLink');
      const sectionTbody = document.querySelector('#sectionTable tbody');
      const addSectionForm = document.getElementById('addSection');
      const sectionFormHeading = document.getElementById('sectionFormHeading');
      const submitSectionBtn = document.getElementById('submitSectionBtn');
      const cancelEditBtn = document.getElementById('cancelEditBtn');
      const currentImagePreview = document.getElementById('currentImagePreview');
      const removeButtonRow = document.getElementById('removeButtonRow');

      // [MOVE-SECTION] new refs
      const movePageRow = document.getElementById('movePageRow');
      const movePageSelect = document.getElementById('movePageSelect');

      /* ── populate page dropdowns ────────────────────────────────────────── */
      const pageOptionsHTML = PAGES.map(p => `<option value="${p}">${p}</option>`).join('');
      pageSel.innerHTML = pageOptionsHTML;
      movePageSelect.innerHTML = pageOptionsHTML; // [MOVE-SECTION]

      /* ── ✅ NEW: Handle URL parameters for deep linking ─────────────────── */
      const params = new URLSearchParams(location.search);
      const slugParam = params.get('slug');
      const editSectionParam = params.get('editSection');
      const addAfterParam = params.get('addAfter');
      const editPageParam = params.get('editPage'); // ✅ NEW: Handle page editing

      // If ?slug=page provided, preselect that page
      if (slugParam && PAGES.includes(slugParam)) {
        pageSel.value = slugParam;
      }

      /* ── helper: keep 🔗 link consistent ────────────────────────────────── */
      function updatePageLink(slug) {
        if (slug === 'rolling-banner') {
          viewPageLink.style.display = 'none';
        } else {
          viewPageLink.href = slug === 'index' ? '/' : `/${slug}.html`;
          viewPageLink.style.display = 'inline-block';

          // ✅ NEW: Show special message when coming from visual editor
          if (editSectionParam || addAfterParam || editPageParam) {
            viewPageLink.textContent = '← Return to Page';
            viewPageLink.style.background = '#e3f2fd';
            viewPageLink.style.padding = '6px 12px';
            viewPageLink.style.borderRadius = '4px';
            viewPageLink.style.border = '1px solid #2196f3';
            viewPageLink.style.textDecoration = 'none';
            viewPageLink.style.color = '#1976d2';
            viewPageLink.style.fontWeight = '500';
          }
        }
      }

      /* ── helper: reset form but KEEP page selection ────────────────────── */
      function resetSectionForm() {
        const slug = pageSel.value; // remember
        addSectionForm.reset(); // native reset (clears <select> too)
        pageSel.value = slug; // restore

        delete addSectionForm.dataset.editId;
        sectionFormHeading.textContent = 'Add a Dynamic Section';
        submitSectionBtn.textContent = 'Add Section';
        cancelEditBtn.style.display = 'none';
        currentImagePreview.style.display = 'none';
        document.querySelector('input[name="removeImage"]').checked = false;

        movePageRow.style.display = 'none'; // [MOVE-SECTION]

        // Add reset logic for button fields
        document.querySelector('[name="buttonLabel"]').value = '';
        document.querySelector('[name="buttonUrl"]').value = '';
        document.querySelector('[name="removeButton"]').checked = false;
        removeButtonRow.style.display = 'none';

        // Add reset logic for team fields
        teamBuilder.style.display = 'none';
        teamMemberList.innerHTML = '';
        teamMembers = [];
        teamData.value = '[]';

        // Reset navigation controls
        showInNav.checked = false;
        navCatRow.style.display = 'none';

        // Reset image field visibility (show for new sections since default is 'standard')
        const sharedFields = document.getElementById('sharedFields');
        if (sharedFields) {
          const imageField = sharedFields.querySelector('label');
          if (imageField && imageField.querySelector('input[name="image"]')) {
            imageField.style.display = 'block';
          }
        }

        toggleFields(); // (function already defined earlier)
        // ensure layout-specific visibility is reapplied
        sectionLayout.dispatchEvent(new Event('change'));
        updatePageLink(slug);
      }

      /* ── show / hide fields & toggle HTML-5 "required" attrs ───────────── */
      function toggleFields() {
        const isBanner = pageSel.value === 'rolling-banner';
        const addSectionForm = document.getElementById('addSection'); // Get reference to the form

        // blocks
        document.getElementById('metaControls').style.display = isBanner ? 'none' : 'block';
        document.getElementById('rollingBannerFields').style.display = isBanner ? 'block' : 'none';

        // required handling
        const headingInput = document.querySelector('#mainHeadingLabel input[name="heading"]'); // Specific heading input
        const standardTextarea = document.querySelector('#standardOnlyFields textarea[name="text"]'); // Specifically the one for standard layout
        const rollingTextarea = document.querySelector('textarea[name="rollingText"]');

        console.log('--- toggleFields DEBUG START ---');
        console.log('isBanner =', isBanner);
        console.log('addSectionForm novalidate BEFORE:', addSectionForm.hasAttribute('novalidate'));

        if (standardTextarea) {
          console.log('standardTextarea element:', standardTextarea);
          console.log('standardTextarea BEFORE: required =', standardTextarea.hasAttribute('required'));
        }
        if (rollingTextarea) {
          console.log('rollingTextarea element:', rollingTextarea);
          console.log('rollingTextarea BEFORE: required =', rollingTextarea.hasAttribute('required'));
        }

        if (isBanner) {
          addSectionForm.setAttribute('novalidate', ''); // Prevent native HTML5 validation for banner form
          if (headingInput) headingInput.removeAttribute('required');
          if (standardTextarea) {
            standardTextarea.removeAttribute('required');
            console.log(
              'standardTextarea AFTER removeAttribute: required =',
              standardTextarea.hasAttribute('required')
            );
          }
          if (rollingTextarea) rollingTextarea.setAttribute('required', '');
        } else {
          addSectionForm.removeAttribute('novalidate'); // Re-enable native HTML5 validation for other forms
          if (headingInput) headingInput.setAttribute('required', '');
          // paragraph only required for Standard layout
          if (standardTextarea) {
            if (sectionLayout.value === 'standard') standardTextarea.setAttribute('required', '');
            else standardTextarea.removeAttribute('required');
            console.log(
              'standardTextarea AFTER set/removeAttribute: required =',
              standardTextarea.hasAttribute('required')
            );
          }
          if (rollingTextarea) rollingTextarea.removeAttribute('required');
        }

        console.log('addSectionForm novalidate AFTER:', addSectionForm.hasAttribute('novalidate'));
        if (standardTextarea) {
          console.log(
            'standardTextarea FINAL: required =',
            standardTextarea.hasAttribute('required'),
            'display =',
            standardTextarea.style.display
          );
        }
        if (rollingTextarea) {
          console.log(
            'rollingTextarea FINAL: required =',
            rollingTextarea.hasAttribute('required'),
            'display =',
            rollingTextarea.style.display
          );
        }
        console.log('--- toggleFields DEBUG END ---');

        // ensure layout-specific visibility is reapplied
        sectionLayout.dispatchEvent(new Event('change'));
      }

      /* ── fetch & render sections for current slug ──────────────────────── */
      let currentSections = []; // cache for edit/delete clicks

      async function loadSections() {
        const r = await fetch(`/api/sections?page=${pageSel.value}`);
        if (!r.ok) return alert(await r.text());

        currentSections = await r.json();
        sectionTbody.innerHTML = '';

        currentSections.forEach((s, i) => {
          const pos = { top: '🔝 Top', middle: '⏺️ Middle', bottom: '🔽 Bottom' }[s.position || 'bottom'];
          const layoutNames = {
            team: 'Team Grid',
            list: 'List Section',
            testimonial: 'Testimonial',
            standard: 'Standard',
          };
          const layout = layoutNames[s.layout] || 'Standard';

          // For team sections, check if any team members have images
          let hasImage = s.image ? true : false;
          if (s.layout === 'team' && s.team && Array.isArray(s.team)) {
            hasImage = s.team.some(member => member.image);
          }

          sectionTbody.insertAdjacentHTML(
            'beforeend',
            `
              <tr>
                <td>${i + 1}</td><td>${s.heading}</td><td>${layout}</td><td>${pos}</td><td>${hasImage ? '✔︎' : ''}</td><td>${s.buttonLabel ? '✔︎' : ''}</td>
                <td>
                  <button class="edit"   data-id="${s._id}" title="Edit">✏️</button>
                  <button class="delete" data-id="${s._id}" title="Delete">🗑️</button>
                </td>
              </tr>`
          );
        });
        toggleFields();
      }

      /* ── dropdown change ───────────────────────────────────────────────── */
      pageSel.addEventListener('change', () => {
        loadSections();
        resetSectionForm(); // preserves the just-chosen slug
        updatePageLink(pageSel.value);
      });

      /* ── cancel button ─────────────────────────────────────────────────── */
      cancelEditBtn.addEventListener('click', resetSectionForm);

      /* ── navigation controls logic ──────────────────────────────────────── */
      const showInNav = document.getElementById('showInNav');
      const navCatRow = document.getElementById('navCatRow');
      const sectionNavCategory = document.getElementById('sectionNavCategory');

      // Show/hide navigation category selector based on checkbox
      showInNav.addEventListener('change', () => {
        navCatRow.style.display = showInNav.checked ? 'block' : 'none';
      });

      /* ── layout controls logic ──────────────────────────────────────────── */
      const sectionLayout = document.getElementById('sectionLayout');
      const teamBuilder = document.getElementById('teamBuilder');
      const addMemberBtn = document.getElementById('addMemberBtn');
      const teamMemberList = document.getElementById('teamMemberList');
      const teamData = document.getElementById('teamData');

      let teamMembers = [];

      // Show / hide blocks & (un)set required attrs whenever layout flips
      const standardOnlyFields = document.getElementById('standardOnlyFields');
      const listBuilder = document.getElementById('listBuilder');
      const testimonialBuilder = document.getElementById('testimonialBuilder');

      sectionLayout.addEventListener('change', () => {
        const layoutValue = sectionLayout.value;
        const isTeam = layoutValue === 'team';
        const isList = layoutValue === 'list';
        const isTestimonial = layoutValue === 'testimonial';
        const isStandard = layoutValue === 'standard';

        /* visibility ---------------------------------------------------- */
        standardOnlyFields.style.display = isStandard ? 'block' : 'none';
        teamBuilder.style.display = isTeam ? 'block' : 'none';
        listBuilder.style.display = isList ? 'block' : 'none';
        testimonialBuilder.style.display = isTestimonial ? 'block' : 'none';

        // Hide shared image field for team sections since images are handled per team member
        const sharedFields = document.getElementById('sharedFields');
        if (sharedFields) {
          const imageField = sharedFields.querySelector('label');
          const imagePreview = document.getElementById('currentImagePreview');
          if (imageField && imageField.querySelector('input[name="image"]')) {
            imageField.style.display = isTeam ? 'none' : 'block';
          }
          if (imagePreview) {
            imagePreview.style.display = isTeam ? 'none' : imagePreview.style.display;
          }
        }

        // Hide image upload for testimonial sections (they use default background)
        const imageLabel = document.querySelector('#sharedFields label');
        const imagePreview = document.getElementById('currentImagePreview');
        if (isTestimonial) {
          if (imageLabel && imageLabel.querySelector('input[name="image"]')) imageLabel.style.display = 'none';
          if (imagePreview) imagePreview.style.display = 'none';
        } else {
          if (imageLabel && imageLabel.querySelector('input[name="image"]') && !isTeam)
            imageLabel.style.display = 'block';
          // imagePreview visibility is handled by existing logic
        }

        /* required attributes ------------------------------------------- */
        const para = standardOnlyFields.querySelector('[name="text"]');

        // Standard text field required only for standard layout
        if (isStandard) {
          para.setAttribute('required', '');
        } else {
          para.removeAttribute('required');
        }

        // Testimonial validation is now handled in form submission
        // No individual required fields since we use dynamic testimonial builder

        // team-card fields
        teamMemberList
          .querySelectorAll('.member-nameRole, .member-bio')
          .forEach(el => (isTeam ? el.setAttribute('required', '') : el.removeAttribute('required')));

        // Cleanup when switching away from specific layouts
        if (!isTeam) {
          teamMemberList.innerHTML = '';
          teamMembers = [];
          teamData.value = '[]';
        }
        if (!isList) {
          document.getElementById('listItemsList').innerHTML = '';
          listItems = [];
          document.getElementById('listItemsData').value = '[]';
        }
        if (!isTestimonial) {
          document.getElementById('testimonialList').innerHTML = '';
          testimonials = [];
          document.getElementById('testimonialsData').value = '[]';
        }
      });

      // Add team member functionality
      function createTeamMemberCard(index) {
        const el = document.createElement('div');
        el.className = 'team-member-form';
        el.style.cssText = 'border:1px solid #ddd;padding:15px;margin:10px 0;border-radius:5px;background:#f9f9f9';

        el.innerHTML = `
                <h4>Team Member ${index + 1}</h4>
                
                <div class="form-group">
                    <label>Name & Role:
                        <input type="text" 
                               class="member-nameRole form-control" 
                               placeholder="e.g. Karen Simpson – Director" 
                               required>
                    </label>
                </div>

                <div class="form-group">
                    <label>Bio:
                        <textarea class="member-bio form-control" 
                                  rows="3" 
                                  required></textarea>
                    </label>
                </div>

                <div class="form-group">
                    <label>Quote (optional):
                        <textarea class="member-quote form-control" 
                                  rows="2" 
                                  placeholder='"Tutoring changes lives…"'></textarea>
                    </label>
                </div>

                <div class="form-group">
                    <label>Image:
                        <input type="file" 
                               class="member-image form-control" 
                               accept="image/*">
                        <div class="image-preview" style="margin-top:10px;max-width:200px;display:none">
                            <img src="" alt="Preview" style="width:100%;border-radius:4px">
                        </div>
                    </label>
                </div>

                <button type="button" 
                        class="remove-member btn btn-danger" 
                        style="margin-top:10px">
                    Remove Member
                </button>
            `;

        // Image preview functionality
        const imageInput = el.querySelector('.member-image');
        const previewDiv = el.querySelector('.image-preview');
        const previewImg = previewDiv.querySelector('img');

        imageInput.addEventListener('change', async e => {
          const file = e.target.files[0];
          if (file) {
            // Reset upload flags when new file is selected
            delete el.dataset.imageUploaded;
            delete el.dataset.existingImage;

            // Show preview immediately
            const reader = new FileReader();
            reader.onload = e => {
              previewImg.src = e.target.result;
              previewDiv.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // Upload immediately to prevent race conditions
            if (!el.dataset.uploading) {
              el.dataset.uploading = 'true';
              try {
                const imagePath = await uploadImage(file, 'team');
                el.dataset.imageUploaded = 'true';
                el.dataset.existingImage = imagePath;
                console.log('Team member image uploaded successfully:', imagePath);
              } catch (err) {
                console.error('Team member image upload failed:', err);
                alert('Image upload failed: ' + err.message);
                // Clear the file input on failure
                imageInput.value = '';
                previewDiv.style.display = 'none';
              } finally {
                delete el.dataset.uploading;
              }
            }
          } else {
            previewDiv.style.display = 'none';
            delete el.dataset.imageUploaded;
            delete el.dataset.existingImage;
          }
          // Only update team data after upload is complete
          updateTeamData();
        });

        // Remove button handler
        el.querySelector('.remove-member').addEventListener('click', () => {
          if (confirm('Are you sure you want to remove this team member?')) {
            el.remove();
            updateTeamData();
          }
        });

        // Field change handlers
        el.querySelectorAll('input, textarea').forEach(input => {
          input.addEventListener('input', updateTeamData);
          input.addEventListener('change', updateTeamData);
        });

        return el;
      }

      function addTeamMember() {
        const card = createTeamMemberCard(teamMemberList.children.length);
        teamMemberList.appendChild(card);
        updateTeamData();
      }

      function updateTeamData() {
        teamMembers = []; // reset

        for (const form of teamMemberList.querySelectorAll('.team-member-form')) {
          const name = form.querySelector('.member-nameRole').value.trim();
          const bio = form.querySelector('.member-bio').value.trim();
          const quote = form.querySelector('.member-quote').value.trim();

          // skip incomplete cards
          if (!name || !bio) continue;

          // Use existing uploaded image path (upload happens per-card now)
          const imagePath = form.dataset.existingImage || '';

          teamMembers.push({ name, bio, quote, image: imagePath });
        }

        teamData.value = JSON.stringify(teamMembers);
        console.log('Team data updated', teamMembers);
      }

      // Wire up the add member button
      addMemberBtn.addEventListener('click', addTeamMember);

      /* ── List Section Functionality ────────────────────────────────────── */
      let listItems = [];
      const addListItemBtn = document.getElementById('addListItemBtn');
      const listItemsList = document.getElementById('listItemsList');
      const listItemsData = document.getElementById('listItemsData');

      function addListItem() {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'list-item-form';
        itemDiv.style.cssText =
          'border:1px solid #ddd;padding:10px;margin:5px 0;border-radius:5px;background:#f9f9f9;display:flex;align-items:center;gap:10px;';

        itemDiv.innerHTML = `
                <input type="text"
                       class="list-item-text form-control"
                       placeholder="Enter list item text..."
                       style="flex:1;"
                       required>
                <button type="button" class="remove-list-item" style="background:#dc3545;color:white;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;">Remove</button>
            `;

        // Add remove functionality
        itemDiv.querySelector('.remove-list-item').addEventListener('click', () => {
          itemDiv.remove();
          updateListData();
        });

        // Update data when text changes
        itemDiv.querySelector('.list-item-text').addEventListener('input', updateListData);

        listItemsList.appendChild(itemDiv);
        updateListData();
      }

      function updateListData() {
        listItems = [];

        for (const form of listItemsList.querySelectorAll('.list-item-form')) {
          const text = form.querySelector('.list-item-text').value.trim();
          if (text) {
            listItems.push(text);
          }
        }

        listItemsData.value = JSON.stringify(listItems);
        console.log('List data updated', listItems);
      }

      // Wire up the add list item button
      addListItemBtn.addEventListener('click', addListItem);

      /* ── Testimonial Section Functionality ────────────────────────────────── */
      let testimonials = [];
      const addTestimonialBtn = document.getElementById('addTestimonialBtn');
      const testimonialList = document.getElementById('testimonialList');
      const testimonialsData = document.getElementById('testimonialsData');

      function addTestimonial() {
        const testimonialDiv = document.createElement('div');
        testimonialDiv.style.cssText =
          'border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9;';

        const testimonialIndex = testimonials.length;
        testimonialDiv.innerHTML = `
                <label>
                    Quote:
                    <textarea class="testimonial-quote" rows="3" placeholder="Enter testimonial quote..." style="width: 100%; margin-bottom: 10px;"></textarea>
                </label>
                <label>
                    Author:
                    <input type="text" class="testimonial-author" placeholder="Author name" style="width: 100%; margin-bottom: 10px;">
                </label>
                <label>
                    Role (optional):
                    <input type="text" class="testimonial-role" placeholder="e.g., Parent, Student" style="width: 100%; margin-bottom: 10px;">
                </label>
                <label>
                    Company/Location (optional):
                    <input type="text" class="testimonial-company" placeholder="e.g., Edinburgh, Glasgow" style="width: 100%; margin-bottom: 10px;">
                </label>
                <label>
                    Rating (optional):
                    <select class="testimonial-rating" style="width: 100%; margin-bottom: 10px;">
                        <option value="">No rating</option>
                        <option value="5">⭐⭐⭐⭐⭐ (5 stars)</option>
                        <option value="4">⭐⭐⭐⭐ (4 stars)</option>
                        <option value="3">⭐⭐⭐ (3 stars)</option>
                        <option value="2">⭐⭐ (2 stars)</option>
                        <option value="1">⭐ (1 star)</option>
                    </select>
                </label>
                <button type="button" class="remove-testimonial" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Remove</button>
            `;

        testimonialList.appendChild(testimonialDiv);

        // Add event listeners
        const inputs = testimonialDiv.querySelectorAll('textarea, input, select');
        inputs.forEach(input => {
          input.addEventListener('input', updateTestimonialData);
        });

        testimonialDiv.querySelector('.remove-testimonial').addEventListener('click', () => {
          testimonialDiv.remove();
          updateTestimonialData();
        });

        // Initialize testimonial data
        testimonials.push({
          quote: '',
          author: '',
          role: '',
          company: '',
          rating: '',
        });

        updateTestimonialData();
      }

      function updateTestimonialData() {
        testimonials = [];
        testimonialList.querySelectorAll('div').forEach(div => {
          const quote = div.querySelector('.testimonial-quote')?.value || '';
          const author = div.querySelector('.testimonial-author')?.value || '';
          const role = div.querySelector('.testimonial-role')?.value || '';
          const company = div.querySelector('.testimonial-company')?.value || '';
          const rating = div.querySelector('.testimonial-rating')?.value || '';

          if (quote.trim() && author.trim()) {
            testimonials.push({ quote, author, role, company, rating });
          }
        });

        testimonialsData.value = JSON.stringify(testimonials);
        console.log('Testimonial data updated', testimonials);
      }

      // Wire up the add testimonial button
      addTestimonialBtn.addEventListener('click', addTestimonial);

      /* ── ✅ NEW: Section ordering cleanup when moving between pages ────── */
      async function cleanupSectionOrdering(sectionId, fromPage, toPage) {
        console.log(`[MOVE-SECTION] Cleaning up ordering: ${sectionId} from ${fromPage} to ${toPage}`);

        try {
          // Remove section from source page ordering
          const removeResponse = await fetch('/api/content-manager?operation=remove-from-order', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              targetPage: fromPage,
              sectionId: sectionId,
            }),
          });

          if (!removeResponse.ok) {
            console.warn(`Failed to remove section from ${fromPage} ordering`);
          }

          // Note: We don't automatically add to target page ordering
          // Let the user reorder manually on the target page
          console.log(`[MOVE-SECTION] Section ordering cleanup completed`);
        } catch (error) {
          console.error('[MOVE-SECTION] Error during ordering cleanup:', error);
          throw error;
        }
      }

      /* ── initial boot ──────────────────────────────────────────────────── */
      await loadSections();
      pageSel.dispatchEvent(new Event('change')); // sets initial link

      /* ── ✅ NEW: Handle deep linking parameters after sections are loaded ── */
      if (editSectionParam) {
        // Auto-edit specific section
        console.log(`[Admin] Auto-editing section: ${editSectionParam}`);
        const section = currentSections.find(s => s._id === editSectionParam);
        if (section) {
          // Simulate clicking the edit button
          setTimeout(() => {
            const editBtn = document.querySelector(`button.edit[data-id="${editSectionParam}"]`);
            if (editBtn) {
              editBtn.click();
              console.log(`[Admin] Auto-clicked edit button for section: ${editSectionParam}`);
            }
          }, 100);
        } else {
          console.warn(`[Admin] Section not found for auto-edit: ${editSectionParam}`);
        }
      }

      /* ── ✅ NEW: Handle page editing parameter ─────────────────────────────── */
      if (editPageParam) {
        // Auto-edit specific page
        console.log(`[Admin] Auto-editing page: ${editPageParam}`);

        // Wait for pages to load, then find and edit the page
        setTimeout(async () => {
          await loadPages(); // Ensure pages are loaded
          const page = allPages.find(p => p.slug === editPageParam);
          if (page) {
            // Simulate clicking the edit button for this page
            const editBtn = document.querySelector(`button.edit-page[data-id="${page._id}"]`);
            if (editBtn) {
              editBtn.click();
              console.log(`[Admin] Auto-clicked edit button for page: ${editPageParam}`);

              // Scroll to the page editing form
              setTimeout(() => {
                document.getElementById('pageForm').scrollIntoView({ behavior: 'smooth' });
              }, 200);
            }
          } else {
            console.warn(`[Admin] Page not found for auto-edit: ${editPageParam}`);
          }
        }, 200);
      } else if (addAfterParam) {
        // Pre-configure new section to be added after specified section
        console.log(`[Admin] Pre-configuring new section after: ${addAfterParam}`);
        const afterSection = currentSections.find(s => s._id === addAfterParam);
        if (afterSection) {
          // Set the position based on the after section's position
          const positionSelect = document.querySelector('[name="position"]');
          if (positionSelect && afterSection.position) {
            positionSelect.value = afterSection.position;
          }
          // Focus on the heading field to start adding
          setTimeout(() => {
            const headingField = document.querySelector('[name="heading"]');
            if (headingField) {
              headingField.focus();
              headingField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }, 100);
        }
      }

      // Handles form submission for both ADD and UPDATE
      addSectionForm.addEventListener('submit', async e => {
        e.preventDefault();
        const fd = new FormData(addSectionForm);

        // Always take the page slug straight from the <select>
        // (or, when editing & moving, from the hidden targetPage field)
        const moveVal = addSectionForm.dataset.editId // only present while editing
          ? fd.get('targetPage')?.trim()
          : '';
        const newPage = moveVal || pageSel.value.trim();
        if (!PAGES.includes(newPage)) {
          return alert('Unknown target page: ' + newPage);
        }
        fd.set('page', newPage);
        fd.delete('targetPage');

        // Add navigation fields
        fd.set('showInNav', showInNav.checked ? 'true' : 'false');
        fd.set('navCategory', sectionNavCategory.value || 'about');

        // Add layout field
        fd.set('layout', sectionLayout.value || 'standard');

        // Handle different layout types
        if (sectionLayout.value === 'team') {
          updateTeamData(); // Ensure team data is current

          if (!teamData.value || teamData.value === '[]') {
            return alert('Please add at least one team member');
          }

          fd.set('text', 'Team members section'); // Placeholder text
          fd.set('team', teamData.value);

          // make absolutely sure a stray top-level file never travels
          fd.delete('image');
        } else if (sectionLayout.value === 'list') {
          updateListData(); // Ensure list data is current

          if (!listItemsData.value || listItemsData.value === '[]') {
            return alert('Please add at least one list item');
          }

          // Build list data structure
          const listData = {
            items: JSON.parse(listItemsData.value),
            listType: document.getElementById('listType').value || 'unordered',
            description: document.getElementById('listDescription').value.trim(),
          };

          fd.set('text', JSON.stringify(listData));
          // List sections now support images
        } else if (sectionLayout.value === 'testimonial') {
          const testimonialsJson = document.getElementById('testimonialsData').value;

          if (!testimonialsJson || testimonialsJson === '[]') {
            return alert('Please add at least one testimonial');
          }

          // ✅ NEW: Validate testimonial JSON before submitting
          try {
            const testimonialData = JSON.parse(testimonialsJson);
            if (!Array.isArray(testimonialData)) {
              return alert('Invalid testimonial data format - must be an array');
            }
            // Validate each testimonial has required fields
            for (let i = 0; i < testimonialData.length; i++) {
              const testimonial = testimonialData[i];
              if (!testimonial.quote || typeof testimonial.quote !== 'string') {
                return alert(`Testimonial ${i + 1} is missing a quote`);
              }
              if (!testimonial.author || typeof testimonial.author !== 'string') {
                return alert(`Testimonial ${i + 1} is missing an author`);
              }
            }
            console.log('✅ Client-side testimonial validation passed:', testimonialData.length, 'testimonials');
          } catch (e) {
            console.error('❌ Invalid testimonial JSON on client:', e.message);
            return alert('Invalid testimonial data format: ' + e.message);
          }

          // Use the testimonials array directly
          fd.set('text', testimonialsJson);
          // Testimonial sections now support background images
        }

        const editId = addSectionForm.dataset.editId;
        const originalPage = pageSel.value; // [MOVE-SECTION] remember original page
        if (editId) {
          fd.append('editId', editId);
        }

        /* rolling-banner special-case */
        if (pageSel.value === 'rolling-banner') {
          const txt = fd.get('rollingText').trim();
          if (!txt) return alert('Please enter news content for the rolling banner');

          fd.set('heading', 'Rolling News');
          fd.set('text', txt);

          /* 💡 remove fields we never want for banner rows */
          ['layout', 'showInNav', 'navCategory', 'imagePath', 'buttonLabel', 'buttonUrl', 'position', 'team'].forEach(
            key => fd.delete(key)
          );

          fd.delete('rollingText');
        }

        /* optional image upload (skip for rolling-banner) */
        if (pageSel.value !== 'rolling-banner') {
          const img = fd.get('image');
          if (img instanceof File && img.size) {
            try {
              const url = await uploadImage(img, 'sections');
              fd.set('imagePath', url); // server expects this field
            } catch (err) {
              return alert(err.message);
            }
          }
        }
        fd.delete('image'); // never send raw file

        /* DEBUG – inspect payload in browser console
               -------------------------------------------------
               for (const [k,v] of fd.entries()) console.log('FD',k,v);
            */

        // Send to the unified POST endpoint
        const r = await fetch('/api/sections', { method: 'POST', body: fd });
        if (!r.ok) {
          return alert(await r.text());
        }

        alert(`Section ${editId ? 'updated' : 'added'} successfully!`);

        // [MOVE-SECTION] If moved, switch the admin view *before* reset
        if (editId && newPage !== originalPage) {
          console.log(`Moving section from ${originalPage} to ${newPage}`);

          // ✅ NEW: Clean up section ordering when moving between pages
          try {
            await cleanupSectionOrdering(editId, originalPage, newPage);
          } catch (error) {
            console.warn('Failed to cleanup section ordering:', error);
            // Don't block the UI, just warn
          }

          pageSel.value = newPage;
          pageSel.dispatchEvent(new Event('change'));
        }

        resetSectionForm();
        loadSections();

        /* refresh banner immediately if we just added news */
        if (pageSel.value === 'rolling-banner') {
          fetch('/api/sections?page=rolling-banner')
            .then(r => r.json())
            .then(list => (tutorBanner.textContent = list.map(s => s.text).join(' | ')))
            .catch(console.error);
        }
      });

      // Handles clicks on EDIT and DELETE buttons
      sectionTbody.addEventListener('click', async e => {
        const id = e.target.dataset.id;
        if (!id) return;

        // Handle DELETE
        if (e.target.classList.contains('delete')) {
          if (!confirm('Are you sure you want to delete this section?')) return;
          const r = await fetch(`/api/sections?id=${id}`, { method: 'DELETE' });
          if (r.ok) {
            if (id === addSectionForm.dataset.editId) resetSectionForm();
            loadSections();
          } else {
            alert(await r.text());
          }
          return; // Stop execution after delete
        }

        // Handle EDIT
        if (e.target.classList.contains('edit')) {
          const section = currentSections.find(s => s._id === id);
          if (!section) return alert('Section not found. Please refresh.');

          // ---- Set state and trigger UI update ----
          addSectionForm.dataset.editId = id;

          // Handle rolling-banner sections differently
          if (pageSel.value === 'rolling-banner') {
            sectionFormHeading.textContent = 'Edit Rolling Banner News';

            // For rolling-banner, populate the rolling text field
            addSectionForm.querySelector('[name="rollingText"]').value = section.text || '';

            // Hide all meta controls for rolling-banner
            document.getElementById('metaControls').style.display = 'none';
            document.getElementById('rollingBannerFields').style.display = 'block';
          } else {
            // Handle regular sections
            sectionLayout.value = section.layout || 'standard';
            sectionLayout.dispatchEvent(new Event('change')); // This shows/hides the correct field blocks

            // ---- Populate common heading field for ALL layouts ----
            addSectionForm.querySelector('[name="heading"]').value = section.heading;

            // ---- Populate form based on layout ----
            if (section.layout === 'team') {
              sectionFormHeading.textContent = 'Edit Team Section'; // UI Nicety

              // Rebuild team member cards
              teamMemberList.innerHTML = '';
              teamMembers = [];

              (section.team || []).forEach((member, index) => {
                const card = createTeamMemberCard(teamMemberList.children.length);

                // Pre-fill fields from the section data
                card.querySelector('.member-nameRole').value = member.name || '';
                card.querySelector('.member-bio').value = member.bio || '';
                card.querySelector('.member-quote').value = member.quote || '';

                // Remember and display the existing image
                if (member.image) {
                  card.dataset.existingImage = member.image; // This prevents the image from being lost on update
                  const previewDiv = card.querySelector('.image-preview');
                  const previewImg = previewDiv.querySelector('img');
                  previewImg.src = member.image;
                  previewDiv.style.display = 'block';
                }
                teamMemberList.appendChild(card);
              });
              updateTeamData(); // Sync the populated data to the hidden input

              // Hide shared image field for team sections since images are handled per team member
              const sharedFields = document.getElementById('sharedFields');
              if (sharedFields) {
                const imageField = sharedFields.querySelector('label');
                const imagePreview = document.getElementById('currentImagePreview');
                if (imageField && imageField.querySelector('input[name="image"]')) {
                  imageField.style.display = 'none';
                }
                if (imagePreview) {
                  imagePreview.style.display = 'none';
                }
              }
            } else if (section.layout === 'list') {
              sectionFormHeading.textContent = 'Edit List Section';

              // Parse list data
              let listData;
              try {
                listData = JSON.parse(section.text);
              } catch (e) {
                listData = { items: [], listType: 'unordered', description: '' };
              }

              // Populate list fields
              document.getElementById('listDescription').value = listData.description || '';
              document.getElementById('listType').value = listData.listType || 'unordered';

              // Rebuild list items
              listItemsList.innerHTML = '';
              listItems = [];

              (listData.items || []).forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'list-item-form';
                itemDiv.style.cssText =
                  'border:1px solid #ddd;padding:10px;margin:5px 0;border-radius:5px;background:#f9f9f9;display:flex;align-items:center;gap:10px;';

                itemDiv.innerHTML = `
                                <input type="text"
                                       class="list-item-text form-control"
                                       value="${item}"
                                       style="flex:1;"
                                       required>
                                <button type="button" class="remove-list-item" style="background:#dc3545;color:white;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;">Remove</button>
                            `;

                // Add remove functionality
                itemDiv.querySelector('.remove-list-item').addEventListener('click', () => {
                  itemDiv.remove();
                  updateListData();
                });

                // Update data when text changes
                itemDiv.querySelector('.list-item-text').addEventListener('input', updateListData);

                listItemsList.appendChild(itemDiv);
              });

              updateListData();

              // Show shared image field for list sections
              const sharedFieldsList = document.getElementById('sharedFields');
              if (sharedFieldsList) {
                const imageField = sharedFieldsList.querySelector('label');
                if (imageField && imageField.querySelector('input[name="image"]')) {
                  imageField.style.display = 'block';
                }
              }
            } else if (section.layout === 'testimonial') {
              sectionFormHeading.textContent = 'Edit Testimonial Section';

              // Parse testimonial data - handle both old single format and new array format
              let testimonialData;
              try {
                testimonialData = JSON.parse(section.text);
              } catch (e) {
                testimonialData = [];
              }

              // Convert old single testimonial format to array format
              if (testimonialData.quote && !Array.isArray(testimonialData)) {
                testimonialData = [testimonialData];
              }

              // Clear existing testimonials and populate with data
              testimonialList.innerHTML = '';
              testimonials = [];

              if (Array.isArray(testimonialData) && testimonialData.length > 0) {
                testimonialData.forEach(testimonial => {
                  addTestimonial();
                  const lastDiv = testimonialList.lastElementChild;
                  lastDiv.querySelector('.testimonial-quote').value = testimonial.quote || '';
                  lastDiv.querySelector('.testimonial-author').value = testimonial.author || '';
                  lastDiv.querySelector('.testimonial-role').value = testimonial.role || '';
                  lastDiv.querySelector('.testimonial-company').value = testimonial.company || '';
                  lastDiv.querySelector('.testimonial-rating').value = testimonial.rating || '';
                });
              } else {
                // Add one empty testimonial if none exist
                addTestimonial();
              }

              updateTestimonialData();

              // Show shared image field for testimonial sections
              const sharedFieldsTestimonial = document.getElementById('sharedFields');
              if (sharedFieldsTestimonial) {
                const imageField = sharedFieldsTestimonial.querySelector('label');
                if (imageField && imageField.querySelector('input[name="image"]')) {
                  imageField.style.display = 'block';
                }
              }
            } else {
              // It's a 'standard' layout
              sectionFormHeading.textContent = 'Edit Dynamic Section'; // UI Nicety
              addSectionForm.querySelector('[name="text"]').value = section.text;

              if (section.image) {
                currentImagePreview.querySelector('img').src = section.image;
                currentImagePreview.style.display = 'block';
              } else {
                currentImagePreview.style.display = 'none';
              }
              document.querySelector('input[name="removeImage"]').checked = false;

              // Show shared image field for non-team sections
              const sharedFields = document.getElementById('sharedFields');
              if (sharedFields) {
                const imageField = sharedFields.querySelector('label');
                if (imageField && imageField.querySelector('input[name="image"]')) {
                  imageField.style.display = 'block';
                }
              }
            }

            // ---- Populate common fields for ALL layouts ----
            // Button fields (moved here so they work for all layout types)
            addSectionForm.querySelector('[name="buttonLabel"]').value = section.buttonLabel || '';
            addSectionForm.querySelector('[name="buttonUrl"]').value = section.buttonUrl || '';
            removeButtonRow.style.display = section.buttonLabel ? 'flex' : 'none';

            // Image handling for all layout types
            if (section.image && section.layout !== 'standard') {
              currentImagePreview.querySelector('img').src = section.image;
              currentImagePreview.style.display = 'block';
            } else if (section.layout !== 'standard') {
              currentImagePreview.style.display = 'none';
            }
            if (section.layout !== 'standard') {
              document.querySelector('input[name="removeImage"]').checked = false;
            }
            addSectionForm.querySelector('[name="position"]').value = section.position || 'bottom';
            showInNav.checked = !!section.showInNav;
            sectionNavCategory.value = section.navCategory || 'about';
            navCatRow.style.display = showInNav.checked ? 'block' : 'none';

            // [MOVE-SECTION] show move control except for banner rows
            if (pageSel.value !== 'rolling-banner') {
              movePageRow.style.display = 'block';
              movePageSelect.value = pageSel.value;
            }
          }

          // ---- Final UI updates for editing ----
          submitSectionBtn.textContent = 'Update Section';
          cancelEditBtn.style.display = 'inline-block';
          addSectionForm.scrollIntoView({ behavior: 'smooth' });
        }
      });

      /* ░░░ 3. PAGE MANAGEMENT ░░░ */
      const pagesTbody = document.querySelector('#pagesTable tbody');
      const pageForm = document.getElementById('pageForm');
      const submitPageBtn = document.getElementById('submitPageBtn');
      const cancelPageEditBtn = document.getElementById('cancelPageEditBtn');
      const pageEditOptions = document.getElementById('pageEditOptions');
      const currentPageImagePreview = document.getElementById('currentPageImagePreview');

      let allPages = [];

      /* helper – clear & restore "create" state */
      function resetPageForm() {
        pageForm.reset();
        delete pageForm.dataset.editId;
        document.getElementById('pageFormHeading').textContent = 'Create a New Page';
        submitPageBtn.textContent = 'Create Page';
        cancelPageEditBtn.style.display = 'none';
        pageEditOptions.style.display = 'none';
        currentPageImagePreview.style.display = 'none';
        currentPageImagePreview.innerHTML = '';
      }

      /* list pages */
      async function loadPages() {
        const r = await fetch('/api/sections?isFullPage=true');
        if (!r.ok) return alert('Error loading pages: ' + (await r.text()));
        allPages = await r.json();
        pagesTbody.innerHTML = '';
        allPages.forEach(p => {
          const row = document.createElement('tr');
          row.innerHTML = `
              <td>${p.heading}</td>
              <td><a href="/page/${p.slug}" target="_blank">/page/${p.slug}</a></td>
              <td>${p.isPublished ? '✅ Published' : '⏸️ Draft'}</td>
              <td>
                <button class="edit-page"    data-id="${p._id}">✏️</button>
                <button class="toggle-page"  data-id="${p._id}" data-pub="${p.isPublished}">${p.isPublished ? '⏸️' : '▶️'}</button>
                <button class="delete-page"  data-id="${p._id}">🗑️</button>
              </td>`;
          pagesTbody.appendChild(row);
        });
      }

      // Load pages on init
      await loadPages();

      /* create OR update */
      pageForm.addEventListener('submit', async e => {
        e.preventDefault();
        const fd = new FormData(pageForm);
        const editId = pageForm.dataset.editId;

        fd.append('isFullPage', 'true');

        // Add navigation category for pages
        const pageNavCategory = document.getElementById('pageNavCategory');
        fd.set('navCategory', pageNavCategory.value || 'about');
        fd.set('isPublished', document.getElementById('isPublished').checked ? 'true' : 'false');
        fd.set('buttonLabel', fd.get('buttonLabel') || '');
        fd.set('buttonUrl', fd.get('buttonUrl') || '');

        /* remove flags */
        if (fd.get('removeButton')) fd.set('removeButton', 'true');
        if (fd.get('removeImage')) fd.set('removeImage', 'true');

        /* upload replacement image */
        const img = document.getElementById('pageImage').files[0];
        if (img) {
          try {
            fd.set('imagePath', await uploadImage(img, 'pages'));
          } catch (err) {
            return alert('Image upload failed: ' + err.message);
          }
        }
        fd.delete('image');

        if (editId) fd.append('editId', editId);

        const r = await fetch('/api/sections', { method: 'POST', body: fd });
        if (!r.ok) return alert(await r.text());

        alert(`Page ${editId ? 'updated' : 'created'} successfully!`);
        resetPageForm();
        loadPages();
      });

      /* table actions */
      pagesTbody.addEventListener('click', async e => {
        const btn = e.target.closest('button');
        if (!btn) return;
        const id = btn.dataset.id;

        /* DELETE */
        if (btn.classList.contains('delete-page')) {
          if (!confirm('Delete this page?')) return;
          const r = await fetch(`/api/sections?id=${id}`, { method: 'DELETE' });
          if (!r.ok) return alert(await r.text());
          loadPages();
          return;
        }

        /* TOGGLE publish */
        if (btn.classList.contains('toggle-page')) {
          const current = btn.dataset.pub === 'true';
          const r = await fetch('/api/sections', {
            method: 'POST',
            body: new URLSearchParams({ editId: id, isPublished: (!current).toString() }),
          });
          if (!r.ok) return alert(await r.text());
          loadPages();
          return;
        }

        /* EDIT */
        if (btn.classList.contains('edit-page')) {
          const p = allPages.find(x => x._id === id);
          if (!p) return alert('Page not found.');

          /* populate form */
          pageForm.dataset.editId = id;
          pageForm.querySelector('[name="slug"]').value = p.slug;
          pageForm.querySelector('[name="heading"]').value = p.heading;
          pageForm.querySelector('[name="text"]').value = p.text;
          pageForm.querySelector('[name="buttonLabel"]').value = p.buttonLabel || '';
          pageForm.querySelector('[name="buttonUrl"]').value = p.buttonUrl || '';
          pageForm.querySelector('[name="isPublished"]').checked = p.isPublished;

          if (p.image) {
            currentPageImagePreview.innerHTML = `<p style="margin-bottom:5px">Current Image:</p>
                 <img src="${p.image}" style="max-width:120px;border-radius:4px">`;
            currentPageImagePreview.style.display = 'block';
          }

          document.getElementById('pageFormHeading').textContent = 'Edit Page';
          submitPageBtn.textContent = 'Update Page';
          cancelPageEditBtn.style.display = 'inline-block';
          pageEditOptions.style.display = 'block';
          pageForm.scrollIntoView({ behavior: 'smooth' });
        }
      });

      /* cancel */
      cancelPageEditBtn.addEventListener('click', resetPageForm);

      /* initial */
      await loadPages();
      resetPageForm(); // just in case
    </script>
  </body>
</html>
