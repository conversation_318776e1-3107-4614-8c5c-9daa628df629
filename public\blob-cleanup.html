<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vercel Blob Storage Cleanup - Tutor Scotland</title>
    <link rel="stylesheet" href="/css/styles2.css" />
    <style>
      .cleanup-container {
        max-width: 800px;
        margin: 50px auto;
        padding: 30px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        color: #856404;
      }

      .danger-box {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        color: #721c24;
      }

      .success-box {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        color: #155724;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px 5px;
        transition: all 0.3s ease;
      }

      .btn-danger {
        background: #dc3545;
        color: white;
      }

      .btn-danger:hover {
        background: #c82333;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background: #5a6268;
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .stat-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
      }

      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #007bff;
      }

      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="cleanup-container">
      <h1>🗑️ Vercel Blob Storage Cleanup</h1>

      <div class="warning-box">
        <h3>⚠️ Development Storage Cleanup</h3>
        <p>
          This tool will help you clean up development images from Vercel Blob storage to free up space. This is useful
          when transitioning from development to production.
        </p>
      </div>

      <div id="stats-section">
        <h3>📊 Current Storage Stats</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number" id="total-images">-</div>
            <div>Total Images</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="total-size">-</div>
            <div>Total Size</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="thumbnails">-</div>
            <div>Thumbnails</div>
          </div>
        </div>
        <button class="btn btn-secondary" onclick="loadStats()">
          <span id="load-stats-text">🔄 Refresh Stats</span>
        </button>
      </div>

      <div class="danger-box">
        <h3>🚨 DANGER ZONE</h3>
        <p><strong>This action cannot be undone!</strong></p>
        <p>This will permanently delete ALL images in the content-images folder, including:</p>
        <ul>
          <li>All uploaded images</li>
          <li>All thumbnails</li>
          <li>Development test images</li>
          <li>Any images currently in use on the website</li>
        </ul>
        <p><strong>Make sure you have backups of any images you want to keep!</strong></p>
      </div>

      <div id="confirmation-section" style="display: none">
        <h3>✅ Final Confirmation</h3>
        <p>Type <code>DELETE ALL IMAGES</code> to confirm:</p>
        <input
          type="text"
          id="confirmation-input"
          placeholder="Type confirmation here..."
          style="width: 100%; padding: 10px; margin: 10px 0"
        />
        <button class="btn btn-danger" onclick="executeDelete()" id="delete-btn" disabled>
          <span id="delete-text">🗑️ DELETE ALL IMAGES</span>
        </button>
        <button class="btn btn-secondary" onclick="cancelDelete()">Cancel</button>
      </div>

      <button class="btn btn-danger" onclick="showConfirmation()" id="start-delete-btn">
        🚨 Start Cleanup Process
      </button>

      <div id="results-section" style="display: none"></div>
    </div>

    <script>
      let currentStats = null;

      // Load storage statistics
      async function loadStats() {
        const loadBtn = document.getElementById('load-stats-text');
        loadBtn.innerHTML = '<span class="loading"></span> Loading...';

        try {
          const response = await fetch('/api/content-manager?operation=list-images&perPage=1000');
          const data = await response.json();

          currentStats = data;

          // Calculate stats
          const totalImages = data.images.length;
          const thumbnails = data.images.filter(img => img.hasThumb).length;
          const totalSize = data.images.reduce((sum, img) => sum + (img.size || 0), 0);

          // Update display
          document.getElementById('total-images').textContent = totalImages;
          document.getElementById('thumbnails').textContent = thumbnails;
          document.getElementById('total-size').textContent = formatBytes(totalSize);

          loadBtn.textContent = '🔄 Refresh Stats';
        } catch (error) {
          console.error('Error loading stats:', error);
          loadBtn.textContent = '❌ Error loading stats';
        }
      }

      function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      function showConfirmation() {
        document.getElementById('confirmation-section').style.display = 'block';
        document.getElementById('start-delete-btn').style.display = 'none';

        // Enable delete button when correct text is typed
        const input = document.getElementById('confirmation-input');
        const deleteBtn = document.getElementById('delete-btn');

        input.addEventListener('input', () => {
          deleteBtn.disabled = input.value !== 'DELETE ALL IMAGES';
        });
      }

      function cancelDelete() {
        document.getElementById('confirmation-section').style.display = 'none';
        document.getElementById('start-delete-btn').style.display = 'inline-block';
        document.getElementById('confirmation-input').value = '';
      }

      async function executeDelete() {
        const deleteBtn = document.getElementById('delete-btn');
        const deleteText = document.getElementById('delete-text');

        deleteBtn.disabled = true;
        deleteText.innerHTML = '<span class="loading"></span> Deleting...';

        try {
          const response = await fetch('/api/content-manager?operation=bulk-delete-images&confirm=DELETE_ALL_IMAGES');
          const result = await response.json();

          // Show results
          const resultsSection = document.getElementById('results-section');
          resultsSection.style.display = 'block';

          if (response.ok) {
            resultsSection.innerHTML = `
                        <div class="success-box">
                            <h3>✅ Cleanup Completed Successfully!</h3>
                            <p><strong>${result.deleted}</strong> files deleted</p>
                            ${result.failed > 0 ? `<p><strong>${result.failed}</strong> files failed to delete</p>` : ''}
                            <p>Total processed: ${result.totalProcessed}</p>
                        </div>
                    `;

            // Refresh stats
            setTimeout(loadStats, 1000);
          } else {
            resultsSection.innerHTML = `
                        <div class="danger-box">
                            <h3>❌ Cleanup Failed</h3>
                            <p>${result.message}</p>
                        </div>
                    `;
          }

          // Hide confirmation section
          document.getElementById('confirmation-section').style.display = 'none';
          document.getElementById('start-delete-btn').style.display = 'inline-block';
        } catch (error) {
          console.error('Delete error:', error);
          document.getElementById('results-section').innerHTML = `
                    <div class="danger-box">
                        <h3>❌ Network Error</h3>
                        <p>Failed to communicate with server: ${error.message}</p>
                    </div>
                `;
          document.getElementById('results-section').style.display = 'block';
        }

        deleteBtn.disabled = false;
        deleteText.textContent = '🗑️ DELETE ALL IMAGES';
      }

      // Load stats on page load
      document.addEventListener('DOMContentLoaded', loadStats);
    </script>
  </body>
</html>
